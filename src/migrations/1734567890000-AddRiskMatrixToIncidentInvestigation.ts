import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRiskMatrixToIncidentInvestigation1734567890000 implements MigrationInterface {
  name = 'AddRiskMatrixToIncidentInvestigation1734567890000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add risk matrix fields to incident_investigation table
    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD COLUMN "riskMatrixId" uuid,
      ADD COLUMN "potentialRiskCellPosition" varchar(10),
      ADD COLUMN "potentialRiskValueMappingId" uuid,
      ADD COLUMN "potentialPriorityMasterId" uuid,
      ADD COLUMN "observedRiskCellPosition" varchar(10),
      ADD COLUMN "observedRiskValueMappingId" uuid,
      ADD COLUMN "observedPriorityMasterId" uuid,
      ADD COLUMN "finalRiskCellPosition" varchar(10),
      ADD COLUMN "finalRiskValueMappingId" uuid,
      ADD COLUMN "finalPriorityMasterId" uuid
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_risk_matrix" 
      FOREIGN KEY ("riskMatrixId") REFERENCES "risk_matrix_master"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_potential_risk_value_mapping" 
      FOREIGN KEY ("potentialRiskValueMappingId") REFERENCES "risk_value_mapping"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_potential_priority_master" 
      FOREIGN KEY ("potentialPriorityMasterId") REFERENCES "priority_master"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_observed_risk_value_mapping" 
      FOREIGN KEY ("observedRiskValueMappingId") REFERENCES "risk_value_mapping"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_observed_priority_master" 
      FOREIGN KEY ("observedPriorityMasterId") REFERENCES "priority_master"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_final_risk_value_mapping" 
      FOREIGN KEY ("finalRiskValueMappingId") REFERENCES "risk_value_mapping"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      ADD CONSTRAINT "FK_incident_investigation_final_priority_master" 
      FOREIGN KEY ("finalPriorityMasterId") REFERENCES "priority_master"("id") ON DELETE SET NULL
    `);

    // Add indexes for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_incident_investigation_risk_matrix_id" 
      ON "incident_investigation" ("riskMatrixId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_incident_investigation_potential_priority" 
      ON "incident_investigation" ("potentialPriorityMasterId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_incident_investigation_observed_priority" 
      ON "incident_investigation" ("observedPriorityMasterId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_incident_investigation_final_priority" 
      ON "incident_investigation" ("finalPriorityMasterId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_incident_investigation_final_priority"`);
    await queryRunner.query(`DROP INDEX "IDX_incident_investigation_observed_priority"`);
    await queryRunner.query(`DROP INDEX "IDX_incident_investigation_potential_priority"`);
    await queryRunner.query(`DROP INDEX "IDX_incident_investigation_risk_matrix_id"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_final_priority_master"`);
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_final_risk_value_mapping"`);
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_observed_priority_master"`);
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_observed_risk_value_mapping"`);
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_potential_priority_master"`);
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_potential_risk_value_mapping"`);
    await queryRunner.query(`ALTER TABLE "incident_investigation" DROP CONSTRAINT "FK_incident_investigation_risk_matrix"`);

    // Drop columns
    await queryRunner.query(`
      ALTER TABLE "incident_investigation" 
      DROP COLUMN "finalPriorityMasterId",
      DROP COLUMN "finalRiskValueMappingId",
      DROP COLUMN "finalRiskCellPosition",
      DROP COLUMN "observedPriorityMasterId",
      DROP COLUMN "observedRiskValueMappingId",
      DROP COLUMN "observedRiskCellPosition",
      DROP COLUMN "potentialPriorityMasterId",
      DROP COLUMN "potentialRiskValueMappingId",
      DROP COLUMN "potentialRiskCellPosition",
      DROP COLUMN "riskMatrixId"
    `);
  }
}
