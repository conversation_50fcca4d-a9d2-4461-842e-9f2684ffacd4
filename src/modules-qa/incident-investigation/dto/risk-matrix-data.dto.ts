import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID, Matches } from 'class-validator';

export class RiskMatrixDataDto {
  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'UUID of the risk matrix to use for assessment'
  })
  @IsOptional()
  @IsUUID('all', { message: 'Risk matrix ID must be a valid UUID' })
  riskMatrixId?: string;

  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'Cell position for potential risk (format: r{row}c{column}, e.g., r3c4)',
    example: 'r3c4'
  })
  @IsOptional()
  @Matches(/^r\d+c\d+$/, { message: 'Invalid cell position format. Use format: r{row}c{column}' })
  potentialRiskCellPosition?: string;

  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'UUID of the risk value mapping for potential risk'
  })
  @IsOptional()
  @IsUUID('all', { message: 'Potential risk value mapping ID must be a valid UUID' })
  potentialRiskValueMappingId?: string;

  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'Cell position for observed risk (format: r{row}c{column}, e.g., r2c3)',
    example: 'r2c3'
  })
  @IsOptional()
  @Matches(/^r\d+c\d+$/, { message: 'Invalid cell position format. Use format: r{row}c{column}' })
  observedRiskCellPosition?: string;

  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'UUID of the risk value mapping for observed risk'
  })
  @IsOptional()
  @IsUUID('all', { message: 'Observed risk value mapping ID must be a valid UUID' })
  observedRiskValueMappingId?: string;

  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'Cell position for final risk (format: r{row}c{column}, e.g., r2c4)',
    example: 'r2c4'
  })
  @IsOptional()
  @Matches(/^r\d+c\d+$/, { message: 'Invalid cell position format. Use format: r{row}c{column}' })
  finalRiskCellPosition?: string;

  @ApiProperty({ 
    type: 'string', 
    required: false,
    description: 'UUID of the risk value mapping for final risk'
  })
  @IsOptional()
  @IsUUID('all', { message: 'Final risk value mapping ID must be a valid UUID' })
  finalRiskValueMappingId?: string;
}
