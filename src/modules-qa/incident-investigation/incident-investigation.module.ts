import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IncidentInvestigationController } from './incident-investigation.controller';
import { IncidentInvestigationService } from './incident-investigation.service';
import { IncidentInvestigationRepository } from './repository/incident-investigation.repository';
import { IncidentInvestigationRemarkRepository } from './repository/incident-investigation-remark.repository';
import { IncidentInvestigationReviewRepository } from './repository/incident-investigation.review.repository';
import { IncidentInvestigationCommentRepository } from './repository/incident-investigation-comment.repository';
import { MicroservicesAsyncModule } from '../../micro-services/async';
import { MicroservicesSyncModule } from '../../micro-services/sync';
import { IncidentInvestigationHistoryRepository } from './repository/incident-investigation-history.repository';
import { InjuryRepository } from '../injury/repository/injury.repository';
import { RiskMatrixMasterRepository } from '../../modules/risk-matrix-master/risk-matrix-master.repository';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      IncidentInvestigationRepository,
      IncidentInvestigationRemarkRepository,
      IncidentInvestigationReviewRepository,
      IncidentInvestigationCommentRepository,
      IncidentInvestigationHistoryRepository,
      InjuryRepository,
      RiskMatrixMasterRepository,
    ]),
    MicroservicesAsyncModule,
    MicroservicesSyncModule,
  ],
  controllers: [IncidentInvestigationController],
  providers: [IncidentInvestigationService],
  exports: [],
})
export class IncidentInvestigationModule {}
