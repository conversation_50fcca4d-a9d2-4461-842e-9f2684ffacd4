import { Injectable, BadRequestException } from '@nestjs/common';
import { BaseError, TokenPayloadModel } from 'svm-nest-lib-v3';
import {
  CreateIncidentInvestigationDto,
  ListIncidentInvestigationDto,
  TitleIncidentInvestigation,
  RiskMatrixDataDto,
} from './dto';
import { IncidentInvestigationRepository } from './repository/incident-investigation.repository';
import { UpdateIncidentInvestigationDto } from './dto/update-incident-investigation.dto';
import { ListIncidentInvestigationRequestDto } from './dto/list-incident-investigation-request.dto';
import { SVMIAMService } from '../../micro-services/sync/svm-iam.service';
import {
  ActionEnum,
  FeatureEnum,
  ROLE_NAME_FIXED,
  SubFeatureEnum,
  StatusCommon,
} from '../../commons/enums';
import { RiskMatrixMasterRepository } from '../../modules/risk-matrix-master/risk-matrix-master.repository';
import { RiskMatrixUtils, ProcessedRiskMatrixData } from './utils/risk-matrix.utils';
import { NotificationProducer } from '../../micro-services/async/notification.producer';
import { EmailProducer } from '../../micro-services/async/email.producer';
import { downloadResource, getTextData, PayloadAGGridDto } from 'src/utils';
import { InjuryRepository } from '../injury/repository/injury.repository';
import { Response } from 'express';

@Injectable()
export class IncidentInvestigationService {
  constructor(
    private readonly incidentInvestigationRepository: IncidentInvestigationRepository,
    private readonly svmIAMService: SVMIAMService,
    private readonly notificationProducer: NotificationProducer,
    private readonly emailProducer: EmailProducer,
    private readonly injuryRepository: InjuryRepository,
    private readonly riskMatrixMasterRepository: RiskMatrixMasterRepository,
  ) {
    this.incidentInvestigationRepository._migrateIncidentAddCreatorForUserAssignment();
  }
  async createIncidentInvestigation(user: TokenPayloadModel, body: CreateIncidentInvestigationDto) {
    // Process risk matrix data if provided
    let processedRiskMatrixData: ProcessedRiskMatrixData | null = null;
    if (body.riskMatrixData) {
      processedRiskMatrixData = await this.processRiskMatrixData(
        body.riskMatrixData,
        user.companyId,
      );
    }

    const {
      dataNoti,
      dataSendMail,
      createIncidentInvestigation,
    } = await this.incidentInvestigationRepository.createIncidentInvestigation(
      user,
      body,
      processedRiskMatrixData,
    );
    if (body?.injuries || body?.injuries?.length > 0) {
      body?.injuries.forEach(async (injury) => {
        const prepareInjury = {
          ...injury,
          incidentInvestigationId: createIncidentInvestigation?.id,
        };
        this.injuryRepository.createInjury(prepareInjury, user);
      });
    }
    if (dataNoti.length > 0) {
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
    }

    if (dataSendMail.length > 0) {
      this.emailProducer.publishEmail(dataSendMail);
    }
    return 1;
  }

  async getDetailIncidentInvestigationById(
    incidentInvestigationId: string,
    user: TokenPayloadModel,
  ) {
    return this.incidentInvestigationRepository.detailIncidentInvestigation(
      incidentInvestigationId,
      user,
    );
  }
  async listIncidentInvestigation(
    query: ListIncidentInvestigationDto,
    user: TokenPayloadModel,
    body?: PayloadAGGridDto,
  ) {
    const { rolePermissions } = await this.svmIAMService.listRolePermissionsByUser(user.id, {
      companyId: user.companyId,
      parentCompanyId: user.parentCompanyId,
    });

    const role: any = await this.svmIAMService.listRoleByUser(user?.id);
    const isVettingManager = role.some(({ name }) => {
      return name === ROLE_NAME_FIXED.VETTING_MANAGER;
    });

    const isRestricted = rolePermissions.some(
      (permission) =>
        permission ===
        `${FeatureEnum.QUALITY_ASSURANCE_INCIDENTS}::${SubFeatureEnum.INCIDENTS}::${ActionEnum.RESTRICTED}`,
    );

    return this.incidentInvestigationRepository.listIncidentInvestigation(
      query,
      user,
      isRestricted,
      isVettingManager,
      body,
    );
  }

  async exportIncidentInvestigation(
    query: ListIncidentInvestigationDto,
    user: TokenPayloadModel,
    body: PayloadAGGridDto,
    res: Response,
  ) {
    const incidents: any = await this.listIncidentInvestigation(query, user, body);

    const data = [];
    incidents?.data?.forEach((item) => {
      data.push({
        [TitleIncidentInvestigation.VESSEL]: getTextData(item.vessel?.name),
        [TitleIncidentInvestigation.COMPANY]: getTextData(
          item.vessel?.vesselDocHolders
            ?.map((i) => (i?.status === 'active' ? i?.company?.name : ''))
            .join(''),
        ),
        [TitleIncidentInvestigation.TYPE_OF_INCIDENT]: getTextData(item.typeOfIncident),
        [TitleIncidentInvestigation.DESCRIPTION]: getTextData(item.description),
        [TitleIncidentInvestigation.INCIDENT_DATE]: getTextData(item.dateTimeOfIncident),
        [TitleIncidentInvestigation.STATUS]: getTextData(item.status),
        [TitleIncidentInvestigation.POTENTIAL_RISK]: getTextData(item.potentialRiskValue),
        [TitleIncidentInvestigation.OBSERVED_RISK]: getTextData(item.observedRiskValue),
        [TitleIncidentInvestigation.IMO_NUMBER]: getTextData(item.vessel?.imoNumber),
        [TitleIncidentInvestigation.CIMO]: getTextData(
          item?.vessel?.vesselDocHolders
            ?.map((i) => (i?.status === 'active' ? i?.company?.companyIMO : ''))
            .join(''),
        ),
        [TitleIncidentInvestigation.VOYAGE_NO]: getTextData(item.voyageNo),
        [TitleIncidentInvestigation.TIME_LOSS]: getTextData(item.timeLossValue),
        [TitleIncidentInvestigation.REMARK]: getTextData(item.remark),
        [TitleIncidentInvestigation.REVIEW_STATUS]: getTextData(item.reviewStatus),
        [TitleIncidentInvestigation.CREATED_DATE]: getTextData(item.createdAt),
        [TitleIncidentInvestigation.CREATED_BY_USER]: getTextData(item.createdUser?.username),
        [TitleIncidentInvestigation.UPDATED_DATE]: getTextData(item.updatedAt),
        [TitleIncidentInvestigation.UPDATED_BY_USER]: getTextData(item.updatedUser?.username),
        [TitleIncidentInvestigation.REF_ID]: getTextData(item.refId),
        [TitleIncidentInvestigation.INCIDENT_MONTH]: getTextData(item.dateTimeOfIncident_Month),
        [TitleIncidentInvestigation.INCIDENT_YEAR]: getTextData(item.dateTimeOfIncident_Year),
      });
    });

    return downloadResource(
      query,
      res,
      'Incidents',
      Object.values(TitleIncidentInvestigation),
      data,
    );
  }

  async deleteIncidentInvestigationById(incidentInvestigationId: string, user: TokenPayloadModel) {
    return this.incidentInvestigationRepository.deleteIncidentInvestigation(
      incidentInvestigationId,
      user.companyId,
    );
  }
  async updateIncidentInvestigationById(
    user: TokenPayloadModel,
    incidentInvestigationId: string,
    body: UpdateIncidentInvestigationDto,
  ) {
    const incidentInvestigationDetail = await this.incidentInvestigationRepository.findOne({
      where: {
        id: incidentInvestigationId,
      },
      select: ['createdUserId'],
    });
    // user in user assignment can update
    const userCanUpdate = await this.incidentInvestigationRepository.checkUserCanUpdateRecord(
      user.id,
      incidentInvestigationId,
    );
    if (!userCanUpdate && incidentInvestigationDetail.createdUserId !== user.id) {
      throw new BaseError({
        status: 403,
        message: 'common.UPDATE_FORBIDDEN',
      });
    }

    // Process risk matrix data if provided
    let processedRiskMatrixData: ProcessedRiskMatrixData | null = null;
    if (body.riskMatrixData) {
      processedRiskMatrixData = await this.processRiskMatrixData(
        body.riskMatrixData,
        user.companyId,
      );
    }

    const {
      dataNoti,
      dataSendMail,
      updateIncidentInvestigation,
    } = await this.incidentInvestigationRepository.updateIncidentInvestigation(
      user,
      incidentInvestigationId,
      body,
      processedRiskMatrixData,
    );
    if (body?.injuries || body?.injuries?.length > 0) {
      body?.injuries.forEach(async (injury) => {
        const prepareInjury = {
          ...injury,
          incidentInvestigationId: updateIncidentInvestigation?.id,
        };
        if (!injury?.id) {
          this.injuryRepository.createInjury(prepareInjury, user);
        } else {
          this.injuryRepository.updateInjury(injury?.id, prepareInjury, user);
        }
      });
    }
    if (dataNoti.length > 0) {
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
    }

    if (dataSendMail.length > 0) {
      this.emailProducer.publishEmail(dataSendMail);
    }
    // console.log(dataSendMail, dataNoti);
    return 1;
  }
  async listAndAggregateIncidentInvestigation(
    query: ListIncidentInvestigationRequestDto,
    token: TokenPayloadModel,
    vesselScreeningId: string,
  ) {
    return this.incidentInvestigationRepository.listAndAggregateIncidentInvestigation(
      query,
      token,
      vesselScreeningId,
    );
  }

  /**
   * Process risk matrix data and validate against active matrix
   * @param riskMatrixData - Risk matrix data from DTO
   * @param companyId - Company ID for matrix lookup
   * @returns Processed risk matrix data with resolved priority IDs
   */
  private async processRiskMatrixData(
    riskMatrixData: RiskMatrixDataDto,
    companyId: string,
  ): Promise<ProcessedRiskMatrixData> {
    // Get active risk matrix for company
    const activeMatrix = await this.riskMatrixMasterRepository.findOne({
      where: { companyId, status: StatusCommon.ACTIVE, deleted: false },
      relations: ['cells', 'valueMappings', 'levelMappings'],
    });

    if (!activeMatrix) {
      throw new BadRequestException('No active risk matrix found for company');
    }

    // Use provided matrix ID or default to active matrix
    const matrixId = riskMatrixData.riskMatrixId || activeMatrix.id;

    // If specific matrix ID provided, validate it exists and is active
    if (riskMatrixData.riskMatrixId && riskMatrixData.riskMatrixId !== activeMatrix.id) {
      const specifiedMatrix = await this.riskMatrixMasterRepository.findOne({
        where: {
          id: riskMatrixData.riskMatrixId,
          companyId,
          status: StatusCommon.ACTIVE,
          deleted: false,
        },
        relations: ['cells', 'valueMappings', 'levelMappings'],
      });

      if (!specifiedMatrix) {
        throw new BadRequestException('Specified risk matrix not found or not active');
      }

      // Use specified matrix for validation
      activeMatrix.cells = specifiedMatrix.cells;
      activeMatrix.valueMappings = specifiedMatrix.valueMappings;
      activeMatrix.levelMappings = specifiedMatrix.levelMappings;
      activeMatrix.rows = specifiedMatrix.rows;
      activeMatrix.columns = specifiedMatrix.columns;
    }

    // Validate cell positions against matrix dimensions
    if (riskMatrixData.potentialRiskCellPosition) {
      RiskMatrixUtils.validateCellPosition(riskMatrixData.potentialRiskCellPosition, activeMatrix);
    }
    if (riskMatrixData.observedRiskCellPosition) {
      RiskMatrixUtils.validateCellPosition(riskMatrixData.observedRiskCellPosition, activeMatrix);
    }
    if (riskMatrixData.finalRiskCellPosition) {
      RiskMatrixUtils.validateCellPosition(riskMatrixData.finalRiskCellPosition, activeMatrix);
    }

    // Validate risk value mappings belong to matrix
    if (riskMatrixData.potentialRiskValueMappingId) {
      RiskMatrixUtils.validateRiskValueMapping(
        riskMatrixData.potentialRiskValueMappingId,
        activeMatrix,
      );
    }
    if (riskMatrixData.observedRiskValueMappingId) {
      RiskMatrixUtils.validateRiskValueMapping(
        riskMatrixData.observedRiskValueMappingId,
        activeMatrix,
      );
    }
    if (riskMatrixData.finalRiskValueMappingId) {
      RiskMatrixUtils.validateRiskValueMapping(
        riskMatrixData.finalRiskValueMappingId,
        activeMatrix,
      );
    }

    // Validate consistency between cell positions and risk value mappings
    if (riskMatrixData.potentialRiskCellPosition && riskMatrixData.potentialRiskValueMappingId) {
      RiskMatrixUtils.validateCellAndValueConsistency(
        riskMatrixData.potentialRiskCellPosition,
        riskMatrixData.potentialRiskValueMappingId,
        activeMatrix,
      );
    }
    if (riskMatrixData.observedRiskCellPosition && riskMatrixData.observedRiskValueMappingId) {
      RiskMatrixUtils.validateCellAndValueConsistency(
        riskMatrixData.observedRiskCellPosition,
        riskMatrixData.observedRiskValueMappingId,
        activeMatrix,
      );
    }
    if (riskMatrixData.finalRiskCellPosition && riskMatrixData.finalRiskValueMappingId) {
      RiskMatrixUtils.validateCellAndValueConsistency(
        riskMatrixData.finalRiskCellPosition,
        riskMatrixData.finalRiskValueMappingId,
        activeMatrix,
      );
    }

    // Resolve priority mappings from risk value mappings
    const processedData: ProcessedRiskMatrixData = {
      riskMatrixId: matrixId,
      potentialRiskCellPosition: riskMatrixData.potentialRiskCellPosition,
      potentialRiskValueMappingId: riskMatrixData.potentialRiskValueMappingId,
      potentialPriorityMasterId: riskMatrixData.potentialRiskValueMappingId
        ? RiskMatrixUtils.resolvePriorityFromRiskValue(
          riskMatrixData.potentialRiskValueMappingId,
          activeMatrix,
        )
        : null,
      observedRiskCellPosition: riskMatrixData.observedRiskCellPosition,
      observedRiskValueMappingId: riskMatrixData.observedRiskValueMappingId,
      observedPriorityMasterId: riskMatrixData.observedRiskValueMappingId
        ? RiskMatrixUtils.resolvePriorityFromRiskValue(
          riskMatrixData.observedRiskValueMappingId,
          activeMatrix,
        )
        : null,
      finalRiskCellPosition: riskMatrixData.finalRiskCellPosition,
      finalRiskValueMappingId: riskMatrixData.finalRiskValueMappingId,
      finalPriorityMasterId: riskMatrixData.finalRiskValueMappingId
        ? RiskMatrixUtils.resolvePriorityFromRiskValue(
          riskMatrixData.finalRiskValueMappingId,
          activeMatrix,
        )
        : null,
    };

    return processedData;
  }
}
