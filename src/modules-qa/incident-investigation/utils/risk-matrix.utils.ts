import { BadRequestException } from '@nestjs/common';
import { RiskMatrixMaster } from '../../../modules/risk-matrix-master/entities/risk-matrix-master.entity';
import { RiskValueMapping } from '../../../modules/risk-matrix-master/entities/risk-value-mapping.entity';

export interface CellPosition {
  row: number;
  col: number;
}

export interface RiskScore {
  likelihood: number;
  consequence: number;
  riskScore: number;
}

export interface ProcessedRiskMatrixData {
  riskMatrixId: string;
  potentialRiskCellPosition?: string;
  potentialRiskValueMappingId?: string;
  potentialPriorityMasterId?: string;
  observedRiskCellPosition?: string;
  observedRiskValueMappingId?: string;
  observedPriorityMasterId?: string;
  finalRiskCellPosition?: string;
  finalRiskValueMappingId?: string;
  finalPriorityMasterId?: string;
}

/**
 * Utility class for risk matrix operations
 */
export class RiskMatrixUtils {
  /**
   * Parse cell position string into row and column numbers
   * @param cellPosition - Cell position in format "r{row}c{column}" (e.g., "r3c4")
   * @returns Object with row and column numbers
   */
  static parseCellPosition(cellPosition: string): CellPosition {
    if (!cellPosition) {
      throw new BadRequestException('Cell position cannot be empty');
    }

    const match = cellPosition.match(/^r(\d+)c(\d+)$/);
    if (!match) {
      throw new BadRequestException('Invalid cell position format. Use format: r{row}c{column}');
    }

    return {
      row: parseInt(match[1], 10),
      col: parseInt(match[2], 10),
    };
  }

  /**
   * Validate cell position against matrix dimensions
   * @param cellPosition - Cell position to validate
   * @param matrix - Risk matrix to validate against
   */
  static validateCellPosition(cellPosition: string, matrix: RiskMatrixMaster): void {
    if (!cellPosition) return;

    const { row, col } = this.parseCellPosition(cellPosition);

    if (row < 1 || row > matrix.rows || col < 1 || col > matrix.columns) {
      throw new BadRequestException(
        `Cell position ${cellPosition} is out of matrix bounds. Matrix size: ${matrix.rows}x${matrix.columns}`,
      );
    }
  }

  /**
   * Calculate risk score from cell position
   * @param cellPosition - Cell position
   * @param matrix - Risk matrix
   * @returns Risk score calculation
   */
  static calculateRiskScore(cellPosition: string, matrix: RiskMatrixMaster): RiskScore {
    const { row, col } = this.parseCellPosition(cellPosition);

    // Assuming row = likelihood, column = consequence
    const likelihood = row;
    const consequence = col;
    const riskScore = likelihood * consequence;

    return { likelihood, consequence, riskScore };
  }

  /**
   * Get risk value mapping from cell position
   * @param cellPosition - Cell position
   * @param matrix - Risk matrix with loaded relationships
   * @returns Risk value mapping or null if not found
   */
  static getRiskValueFromCell(
    cellPosition: string,
    matrix: RiskMatrixMaster,
  ): RiskValueMapping | null {
    if (!matrix.cells || !matrix.valueMappings) {
      throw new BadRequestException('Matrix must have cells and valueMappings loaded');
    }

    const cell = matrix.cells.find(
      (c) => c.cellPositions && c.cellPositions.includes(cellPosition),
    );

    if (!cell || !cell.riskValueMappingId) {
      return null;
    }

    return matrix.valueMappings.find((vm) => vm.id === cell.riskValueMappingId) || null;
  }

  /**
   * Resolve priority master ID from risk value mapping ID
   * @param riskValueMappingId - Risk value mapping ID
   * @param matrix - Risk matrix with loaded relationships
   * @returns Priority master ID or null if not found
   */
  static resolvePriorityFromRiskValue(
    riskValueMappingId: string,
    matrix: RiskMatrixMaster,
  ): string | null {
    if (!riskValueMappingId || !matrix.levelMappings) {
      return null;
    }

    const levelMapping = matrix.levelMappings.find(
      (lm) => lm.riskValueMappingIds && lm.riskValueMappingIds.includes(riskValueMappingId),
    );

    return levelMapping?.priorityMasterId || null;
  }

  /**
   * Validate that risk value mapping belongs to the specified matrix
   * @param riskValueMappingId - Risk value mapping ID to validate
   * @param matrix - Risk matrix to validate against
   */
  static validateRiskValueMapping(riskValueMappingId: string, matrix: RiskMatrixMaster): void {
    if (!riskValueMappingId) return;

    if (!matrix.valueMappings) {
      throw new BadRequestException('Matrix must have valueMappings loaded for validation');
    }

    const isValid = matrix.valueMappings.some((vm) => vm.id === riskValueMappingId);
    if (!isValid) {
      throw new BadRequestException(
        `Risk value mapping ${riskValueMappingId} does not belong to matrix ${matrix.id}`,
      );
    }
  }

  /**
   * Validate consistency between cell position and risk value mapping
   * @param cellPosition - Cell position
   * @param riskValueMappingId - Risk value mapping ID
   * @param matrix - Risk matrix with loaded relationships
   */
  static validateCellAndValueConsistency(
    cellPosition: string,
    riskValueMappingId: string,
    matrix: RiskMatrixMaster,
  ): void {
    if (!cellPosition || !riskValueMappingId) return;

    const cellRiskValue = this.getRiskValueFromCell(cellPosition, matrix);
    if (cellRiskValue && cellRiskValue.id !== riskValueMappingId) {
      throw new BadRequestException(
        `Cell position ${cellPosition} does not match risk value mapping ${riskValueMappingId}`,
      );
    }
  }
}
