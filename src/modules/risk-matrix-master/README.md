# Risk Matrix Master Module

## Overview
The Risk Matrix Master module allows users to define and manage risk matrices as defined by the company. This module provides a complete CRUD interface for managing risk matrix configurations.

**Note**: The risk matrix supports multiple value selection through the `riskValueMappingIds` array in level mappings, allowing flexible mapping of risk values to priority levels.

## Features

### 1. Create New Risk Matrix
- Users can define the number of rows and columns for the matrix
- Both fields accept only positive integers and are mandatory
- Only one risk matrix can be active at a time per company
- Save button is enabled only when both rows and columns fields are populated

### 2. List Risk Matrices
- Displays a list/grid of previously created risk matrix records
- Shows Matrix ID, Rows, Columns, Status (Active/Inactive), Created Date, Created By
- Supports filtering by status (Active/Inactive)
- Supports pagination and sorting
- Search functionality for rows/columns

### 3. View Risk Matrix Details
- View detailed information about a specific risk matrix
- Shows creator and updater information
- Displays creation and update timestamps

### 4. Update Risk Matrix
- Modify existing risk matrix configurations
- Maintains business rule: only one active matrix per company
- Tracks update history

### 5. Delete Risk Matrix
- Soft delete functionality
- Maintains data integrity

## API Endpoints

### POST `/risk-matrix-master`
Create a new risk matrix

**Request Body:**
```json
{
  "matrixCode": "RM001",
  "rows": 4,
  "columns": 4,
  "rowsName": ["Very Low", "Low", "Medium", "High"],
  "columnsName": ["Negligible", "Minor", "Moderate", "Major"],
  "status": "active", // optional, defaults to "active"
  "cells": [ // optional
    {
      "riskValueMappingId": "uuid-of-value-mapping",
      "cellPositions": ["r1c1", "r2c2", "r3c3"]
    }
  ],
  "valueMappings": [ // optional
    {
      "riskValueMappingId": "uuid-1", // optional
      "riskValue": "text",
      "color": "#0000FF"
    }
  ],
  "levelMappings": [ // optional
    {
      "priorityMasterId": "uuid-priority", // optional
      "riskValueMappingIds": ["uuid-1", "uuid-2"]
    }
  ]
}
```

### GET `/risk-matrix-master`
List risk matrices with pagination and filtering

**Query Parameters:**
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)
- `status`: Filter by status (active/inactive)
- `content`: Search in rows/columns
- `sort`: Sort fields (e.g., "rows:1;columns:-1")
- `createdAtFrom`: Date range filter start
- `createdAtTo`: Date range filter end

### GET `/risk-matrix-master/:id`
Get risk matrix details by ID

### PUT `/risk-matrix-master/:id`
Update an existing risk matrix

**Request Body:**
```json
{
  "rows": 6,
  "columns": 4,
  "status": "inactive"
}
```

### DELETE `/risk-matrix-master/:id`
Delete a risk matrix (soft delete)

## Validation Rules

1. **Matrix Code**: Must be unique per company, max 50 characters, required
2. **Rows**: Must be a positive integer (minimum 1), required
3. **Columns**: Must be a positive integer (minimum 1), required
4. **Status**: Must be either "active" or "inactive", optional (defaults to "active")
5. **Row Names**: Array of strings, optional
6. **Column Names**: Array of strings, optional
7. **Risk Value**: Max 100 characters per value, required for value mappings
8. **Colors**: Must be valid hex color codes (7 characters including #), optional
9. **Cell Positions**: Array of strings in format ["r1c1", "r2c2"], optional
10. **Risk Value Mapping ID**: Must be valid UUID when provided, optional
11. **Priority Master ID**: Must be valid UUID when provided, optional
12. **Business Rule**: Only one risk matrix can be active per company at any time

## Database Schema

### `RiskMatrixMaster` Entity
- `id`: UUID primary key
- `matrixCode`: Unique matrix code per company (varchar 50)
- `rows`: Number of rows (integer)
- `columns`: Number of columns (integer)
- `rowsName`: Array of row names (simple-array)
- `columnsName`: Array of column names (simple-array)
- `status`: Status enum (active/inactive)
- `companyId`: Company UUID
- `createdUserId`: Creator user UUID
- `updatedUserId`: Updater user UUID
- Standard audit fields from `IdentifyEntity`

### `RiskMatrixCell` Entity
- `id`: UUID primary key
- `riskMatrixId`: Foreign key to RiskMatrixMaster
- `riskValueMappingId`: Foreign key to RiskValueMapping (nullable)
- `cellPositions`: Array of cell positions like ['r1c1', 'r2c2'] (simple-array)
- Standard audit fields from `IdentifyEntity`

### `RiskValueMapping` Entity
- `id`: UUID primary key
- `riskMatrixId`: Foreign key to RiskMatrixMaster
- `riskValue`: Unique value key per matrix (varchar 100)
- `color`: Hex color code (varchar 7, nullable)
- Standard audit fields from `IdentifyEntity`

### `RiskLevelMapping` Entity
- `id`: UUID primary key
- `riskMatrixId`: Foreign key to RiskMatrixMaster
- `riskValueMappingIds`: Array of RiskValueMapping UUIDs (uuid array)
- `priorityMasterId`: Foreign key to PriorityMaster (nullable)
- Standard audit fields from `IdentifyEntity`

## Entity Relationships

1. **RiskMatrixMaster** is the main entity that defines the matrix structure
2. **RiskMatrixCell** defines which cells in the matrix map to which risk values
3. **RiskValueMapping** defines the available risk values and their colors
4. **RiskLevelMapping** maps multiple risk values to priority levels for flexible risk assessment
5. **PriorityMaster** provides the priority levels that can be associated with risk mappings

The relationship supports multiple value selection where a single level mapping can reference multiple risk value mappings through the `riskValueMappingIds` array.

## Permissions

The module uses the following permission structure:
- Feature: `Configuration::Common::Risk Matrix`
- Actions: CREATE, VIEW, UPDATE, DELETE

## Error Handling

- Validates positive integers for rows and columns
- Prevents multiple active matrices per company
- Returns appropriate HTTP status codes and error messages
- Handles not found scenarios gracefully

## Usage Example

```typescript
// Create a new risk matrix
const newMatrix = await riskMatrixService.createRiskMatrix({
  matrixCode: 'RM001',
  rows: 5,
  columns: 5,
  status: 'active'
}, token);

// List matrices with filtering
const matrices = await riskMatrixService.listRiskMatrix({
  page: 1,
  pageSize: 10,
  status: 'active'
}, token);
```