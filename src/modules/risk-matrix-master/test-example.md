# Risk Matrix Master - Test Examples

**Note**: These examples reflect the current DTO structure which supports multiple value selection through `riskValueMappingIds` arrays and uses `priorityMasterId` for level mappings.

## Example 1: Create a Complete Risk Matrix

```bash
POST /risk-matrix-master
Content-Type: application/json
Authorization: Bearer <token>

{
  "matrixCode": "RM001",
  "rows": 4,
  "columns": 4,
  "rowsName": [
        "Probability",
        "Likelihood",
        "Frequency",
        "Occurrence"
    ],
    "columnsName": [
        "Impact",
        "Severity",
        "Consequence",
        "Effect"
    ],
  "cells": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "cellPositions": ["r1c1", "r1c2"]
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "cellPositions": ["r4c4"]
    }
  ],
  "valueMappings": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "riskValue": "text",
      "color": "#0000FF"
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "riskValue": "test2",
      "color": "#FF00FF"
    }
  ],
  "levelMappings": [
    {
      "priorityMasterId": "negligible-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping"]
    },
    {
      "priorityMasterId": "low-priority-uuid",
      "riskValueMappingIds": ["uuid-of-test2-mapping"]
    },
    {
      "priorityMasterId": "medium-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping", "uuid-of-test2-mapping"]
    }
  ]
}
```


## Testing Workflow

1. **Create Matrix**: Use Example 1 to create a complete risk matrix
2. **Verify Creation**: Use GET /risk-matrix-master to list matrices
3. **Test Validation**: Try creating duplicate matrix codes (should fail)
4. **Test Business Rules**: Try creating multiple active matrices (should fail)
